{"name": "vocabulator", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint"}, "dependencies": {"@capacitor/android": "5.0.0", "@capacitor/app": "5.0.0", "@capacitor/core": "5.0.0", "@capacitor/haptics": "5.0.0", "@capacitor/keyboard": "5.0.0", "@capacitor/status-bar": "5.0.0", "@headlessui/vue": "^1.7.16", "@ionic/vue": "^8.0.0", "@ionic/vue-router": "^8.0.0", "@neoconfetti/vue": "^2.2.1", "@pinia/colada": "^0.14.1", "@vueuse/rxjs": "^10.1.2", "axios": "^1.6.3", "dexie": "^3.2.4", "idb": "^7.1.1", "ionicons": "^7.0.0", "pinia": "^2.3.1", "rxjs": "^7.8.1", "vee-validate": "^4.12.5", "vue": "^3.2.36", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@capacitor/cli": "5.0.0", "@types/dexie": "^1.3.1", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.3.0", "eslint": "^8.35.0", "eslint-plugin-vue": "^9.9.0", "jsdom": "^21.1.0", "typescript": "^4.9.3", "vite": "^4.5.5", "vite-plugin-pwa": "^0.14.7", "vitest": "^0.29.2", "vue-tsc": "^1.0.24"}, "description": "The very first app"}