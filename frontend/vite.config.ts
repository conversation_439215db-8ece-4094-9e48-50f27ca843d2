import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(), 
    VitePWA({ registerType: 'autoUpdate' })],
    build: {
      target: 'esnext'
    },
    server: {
      hmr: {
          host: 'localhost',
          port: 8080,
      }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  }
})
