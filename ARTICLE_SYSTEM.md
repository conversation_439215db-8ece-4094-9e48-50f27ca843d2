# Article System Documentation

## Overview

This application now uses a file-based article system that stores content in individual JSON files. This approach provides excellent SEO performance, easy content management, and dynamic loading without hardcoded file names.

## Architecture

### File Structure
```
public/articles/
├── manifest.json                    # List of all articles with metadata
├── best-restaurants-paris.json     # Individual article files
├── family-activities-tokyo.json
├── hidden-gems-barcelona.json
└── ...
```

### Key Components

1. **ArticleService** (`src/services/ArticleService.ts`)
   - Handles all article loading and caching
   - Provides methods for filtering, searching, and retrieving articles
   - Implements intelligent caching with 5-minute expiry

2. **Article Types** (`src/types/Article.ts`)
   - TypeScript interfaces for type safety
   - Comprehensive article structure with SEO metadata

3. **React Hooks** (`src/hooks/useArticles.ts`)
   - Custom hooks for data fetching with React Query
   - Automatic caching and background updates
   - Error handling and loading states

4. **SEO Components** (`src/components/SEOHead.tsx`)
   - Dynamic meta tags based on article content
   - Open Graph and Twitter Card support
   - Structured data (JSON-LD) for search engines

## Article File Format

Each article is a JSON file with the following structure:

```json
{
  "id": "unique-id",
  "slug": "url-friendly-slug",
  "title": "Article Title",
  "shortDescription": "Brief description for cards",
  "tag": "category",
  "likes": 123,
  "image": "unsplash-image-id",
  "author": "Author Name",
  "publishedDate": "2024-01-15T10:00:00Z",
  "lastModified": "2024-01-15T10:00:00Z",
  "readingTime": 8,
  "featured": true,
  "seo": {
    "title": "SEO optimized title",
    "description": "Meta description",
    "keywords": ["keyword1", "keyword2"],
    "ogTitle": "Open Graph title",
    "ogDescription": "OG description",
    "ogImage": "https://example.com/image.jpg",
    "structuredData": { /* JSON-LD structured data */ }
  },
  "content": {
    "introduction": "Article introduction",
    "places": [
      {
        "name": "Place Name",
        "rating": 5,
        "keywords": ["tag1", "tag2"],
        "description": "Detailed description",
        "address": "Full address",
        "website": "https://example.com",
        "priceRange": "€€€",
        "openingHours": "9:00-18:00",
        "image": "unsplash-image-id"
      }
    ],
    "amazonProducts": [
      {
        "name": "Product Name",
        "url": "https://amazon.com/...",
        "price": "$19.99",
        "description": "Product description",
        "image": "product-image.jpg",
        "affiliateId": "affiliate-id"
      }
    ],
    "additionalSections": [
      {
        "title": "Section Title",
        "content": "Section content"
      }
    ],
    "conclusion": "Article conclusion"
  }
}
```

## SEO Features

### 1. Dynamic Meta Tags
- Title, description, and keywords based on article content
- Open Graph tags for social media sharing
- Twitter Card support
- Canonical URLs to prevent duplicate content

### 2. Structured Data
- JSON-LD structured data for search engines
- Article schema with author, publisher, and publication dates
- Website schema with search functionality

### 3. Sitemap Generation
- Automatic sitemap generation from article data
- Proper priority and change frequency settings
- Support for featured articles with higher priority

### 4. Performance Optimization
- Intelligent caching with configurable expiry
- Lazy loading of article content
- Image optimization with Unsplash URLs
- React Query for efficient data fetching

## Adding New Articles

### Automatic Manifest Generation ✨

The system now **automatically generates** the `manifest.json` file! You only need to:

1. **Create Article File**: Add a new JSON file in `public/articles/` with the slug as filename
2. **Follow the format**: Ensure all required fields are present (id, slug, title)
3. **Done!**: The manifest is automatically updated

### Manual Commands (if needed)

```bash
# Generate manifest once
npm run manifest:generate

# Watch for changes and auto-regenerate (development)
npm run manifest:watch

# Development server (includes auto-watch)
npm run dev
```

### How It Works

- **Development**: Vite plugin watches the articles directory and regenerates manifest on file changes
- **Build**: Manifest is generated automatically before building
- **Hot Reload**: Changes trigger automatic page refresh in development

### Required Article Fields

For automatic manifest generation, ensure your article JSON includes:

```json
{
  "id": "unique-id",           // Required
  "slug": "article-slug",      // Required
  "title": "Article Title",    // Required
  "shortDescription": "...",   // Optional
  "tag": "category",          // Optional (defaults to 'general')
  "likes": 0,                 // Optional (defaults to 0)
  "image": "unsplash-id",     // Optional
  "publishedDate": "2024-...", // Optional (defaults to current date)
  "featured": false,          // Optional (defaults to false)
  // ... rest of article content
}
```

### Validation

The manifest generator will:
- ✅ Skip files missing required fields (with warnings)
- ✅ Sort articles by publication date (newest first)
- ✅ Provide detailed console output during generation
- ✅ Handle JSON parsing errors gracefully

## Performance Considerations

### Caching Strategy
- **Article Service Cache**: 5-minute expiry for individual articles
- **React Query Cache**: 5-minute stale time, 10-minute garbage collection
- **Manifest Cache**: Cached until page refresh

### Image Optimization
- Uses Unsplash's URL parameters for automatic resizing
- Responsive images with appropriate sizes for different viewports
- Lazy loading for better performance

### Bundle Size
- Articles are loaded on-demand, not bundled with the application
- Only metadata is loaded initially for the article list
- Full content is fetched when viewing individual articles

## Development Workflow

### 1. Local Development
```bash
npm run dev
```

### 2. Adding Articles
1. Create article JSON file
2. Update manifest.json
3. Test locally
4. Deploy to production

### 3. SEO Testing
- Use browser dev tools to inspect meta tags
- Test structured data with Google's Rich Results Test
- Verify sitemap generation

## Production Deployment

### Environment Variables
Set the base URL for production:
```env
VITE_BASE_URL=https://your-domain.com
```

### Build Process
```bash
npm run build
```

The build process will:
- Optimize all assets
- Generate static files
- Prepare articles for serving

### CDN Considerations
- Article JSON files can be cached by CDN
- Set appropriate cache headers for article files
- Consider using a CDN for image delivery

## Monitoring and Analytics

### Performance Metrics
- Monitor article loading times
- Track cache hit rates
- Measure SEO performance

### SEO Monitoring
- Track search engine rankings
- Monitor structured data validity
- Analyze click-through rates from search results

## Future Enhancements

### Planned Features
1. **Content Management Interface**: Web-based editor for articles
2. **Automated Sitemap Generation**: Build-time sitemap creation
3. **Image Optimization**: Local image processing and optimization
4. **Multi-language Support**: Internationalization for articles
5. **Advanced Search**: Full-text search with indexing
6. **Analytics Integration**: Built-in performance tracking

### Scalability Considerations
- Consider moving to a headless CMS for larger content volumes
- Implement search indexing for better search performance
- Add content versioning and revision history
- Implement automated content validation
