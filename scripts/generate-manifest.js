#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const ARTICLES_DIR = path.join(__dirname, '../public/articles');
const MANIFEST_PATH = path.join(ARTICLES_DIR, 'manifest.json');

/**
 * Generate manifest.json from all article files in the articles directory
 */
async function generateManifest() {
  try {
    console.log('🔄 Generating article manifest...');

    // Read all files in articles directory
    const files = fs.readdirSync(ARTICLES_DIR);
    const articleFiles = files.filter(file => 
      file.endsWith('.json') && file !== 'manifest.json'
    );

    console.log(`📄 Found ${articleFiles.length} article files`);

    const manifestEntries = [];

    // Process each article file
    for (const file of articleFiles) {
      const filePath = path.join(ARTICLES_DIR, file);
      
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const article = JSON.parse(content);

        // Validate required fields
        if (!article.id || !article.slug || !article.title) {
          console.warn(`⚠️  Skipping ${file}: Missing required fields (id, slug, title)`);
          continue;
        }

        // Extract manifest entry from article
        const manifestEntry = {
          id: article.id,
          slug: article.slug,
          title: article.title,
          shortDescription: article.shortDescription || '',
          tag: article.tag || 'general',
          likes: article.likes || 0,
          image: article.image || '',
          publishedDate: article.publishedDate || new Date().toISOString(),
          featured: article.featured || false
        };

        manifestEntries.push(manifestEntry);
        console.log(`✅ Processed: ${article.title}`);

      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message);
      }
    }

    // Sort by publication date (newest first)
    manifestEntries.sort((a, b) => 
      new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime()
    );

    // Write manifest file
    fs.writeFileSync(
      MANIFEST_PATH, 
      JSON.stringify(manifestEntries, null, 2),
      'utf8'
    );

    console.log(`🎉 Generated manifest with ${manifestEntries.length} articles`);
    console.log(`📝 Manifest saved to: ${MANIFEST_PATH}`);

    // Generate summary
    const tagCounts = manifestEntries.reduce((acc, article) => {
      acc[article.tag] = (acc[article.tag] || 0) + 1;
      return acc;
    }, {});

    console.log('\n📊 Article Summary:');
    Object.entries(tagCounts).forEach(([tag, count]) => {
      console.log(`   ${tag}: ${count} articles`);
    });

    const featuredCount = manifestEntries.filter(a => a.featured).length;
    console.log(`   Featured: ${featuredCount} articles`);

  } catch (error) {
    console.error('❌ Error generating manifest:', error);
    process.exit(1);
  }
}

/**
 * Watch for changes in articles directory (development mode)
 */
function watchArticles() {
  console.log('👀 Watching articles directory for changes...');
  
  fs.watch(ARTICLES_DIR, { recursive: false }, (eventType, filename) => {
    if (filename && filename.endsWith('.json') && filename !== 'manifest.json') {
      console.log(`📝 Detected change in ${filename}, regenerating manifest...`);
      generateManifest();
    }
  });
}

// CLI handling
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'watch':
    generateManifest().then(() => watchArticles());
    break;
  case 'generate':
  default:
    generateManifest();
    break;
}
