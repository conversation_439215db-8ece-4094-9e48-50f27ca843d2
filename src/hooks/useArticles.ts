import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { articleService } from '../services/ArticleService';
import { Article, ArticleListItem } from '../types/Article';

/**
 * Hook for fetching all articles
 */
export const useArticles = () => {
  return useQuery({
    queryKey: ['articles'],
    queryFn: () => articleService.getArticlesList(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for fetching a specific article by slug
 */
export const useArticle = (slug: string) => {
  return useQuery({
    queryKey: ['article', slug],
    queryFn: () => articleService.getArticle(slug),
    enabled: !!slug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for fetching articles by tag with search functionality
 */
export const useFilteredArticles = (selectedTag: string, searchQuery: string) => {
  const { data: allArticles, isLoading, error } = useArticles();
  const [filteredArticles, setFilteredArticles] = useState<ArticleListItem[]>([]);

  useEffect(() => {
    if (!allArticles) {
      setFilteredArticles([]);
      return;
    }

    let filtered = allArticles;

    // Filter by tag
    if (selectedTag !== 'all') {
      filtered = filtered.filter(article => article.tag === selectedTag);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase();
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(searchTerm) ||
        article.shortDescription.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredArticles(filtered);
  }, [allArticles, selectedTag, searchQuery]);

  return {
    articles: filteredArticles,
    isLoading,
    error,
    totalCount: allArticles?.length || 0,
    filteredCount: filteredArticles.length
  };
};

/**
 * Hook for fetching available tags
 */
export const useTags = () => {
  return useQuery({
    queryKey: ['tags'],
    queryFn: () => articleService.getAvailableTags(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook for fetching featured articles
 */
export const useFeaturedArticles = () => {
  return useQuery({
    queryKey: ['featured-articles'],
    queryFn: () => articleService.getFeaturedArticles(),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for search functionality with debouncing
 */
export const useArticleSearch = (query: string, debounceMs: number = 300) => {
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, debounceMs]);

  return useQuery({
    queryKey: ['search-articles', debouncedQuery],
    queryFn: () => articleService.searchArticles(debouncedQuery),
    enabled: debouncedQuery.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};
