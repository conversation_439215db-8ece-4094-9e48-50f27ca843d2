export interface Place {
  name: string;
  rating: number;
  keywords: string[];
  description?: string;
  address?: string;
  website?: string;
  priceRange?: string;
  openingHours?: string;
  image?: string;
}

export interface AmazonProduct {
  name: string;
  url: string;
  price: string;
  description?: string;
  image?: string;
  affiliateId?: string;
}

export interface SEOMetadata {
  title: string;
  description: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl?: string;
  structuredData?: Record<string, any>;
}

export interface ArticleMetadata {
  id: string;
  slug: string;
  title: string;
  shortDescription: string;
  tag: string;
  likes: number;
  image: string;
  author?: string;
  publishedDate: string;
  lastModified: string;
  readingTime?: number;
  featured?: boolean;
  seo: SEOMetadata;
}

export interface Article extends ArticleMetadata {
  content: {
    introduction?: string;
    conclusion?: string;
    places: Place[];
    amazonProducts: AmazonProduct[];
    additionalSections?: {
      title: string;
      content: string;
    }[];
  };
}

export interface ArticleListItem {
  id: string;
  slug: string;
  title: string;
  shortDescription: string;
  tag: string;
  likes: number;
  image: string;
  publishedDate: string;
  featured?: boolean;
}
