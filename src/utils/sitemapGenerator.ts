import { articleService } from '../services/ArticleService';

export interface SitemapEntry {
  url: string;
  lastModified: string;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
}

export class SitemapGenerator {
  private baseUrl: string;

  constructor(baseUrl: string = 'https://listylists.com') {
    this.baseUrl = baseUrl;
  }

  /**
   * Generate sitemap entries for all articles
   */
  async generateSitemapEntries(): Promise<SitemapEntry[]> {
    const entries: SitemapEntry[] = [];

    // Add homepage
    entries.push({
      url: this.baseUrl,
      lastModified: new Date().toISOString(),
      changeFrequency: 'daily',
      priority: 1.0
    });

    // Add article pages
    try {
      const articles = await articleService.getArticlesList();
      
      for (const article of articles) {
        entries.push({
          url: `${this.baseUrl}/article/${article.slug}`,
          lastModified: article.publishedDate,
          changeFrequency: 'weekly',
          priority: article.featured ? 0.9 : 0.8
        });
      }

      // Add tag pages
      const tags = await articleService.getAvailableTags();
      for (const tag of tags) {
        if (tag !== 'all') {
          entries.push({
            url: `${this.baseUrl}/tag/${tag}`,
            lastModified: new Date().toISOString(),
            changeFrequency: 'weekly',
            priority: 0.7
          });
        }
      }
    } catch (error) {
      console.error('Error generating sitemap entries:', error);
    }

    return entries;
  }

  /**
   * Generate XML sitemap
   */
  async generateXMLSitemap(): Promise<string> {
    const entries = await this.generateSitemapEntries();
    
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    
    for (const entry of entries) {
      xml += '  <url>\n';
      xml += `    <loc>${entry.url}</loc>\n`;
      xml += `    <lastmod>${entry.lastModified}</lastmod>\n`;
      xml += `    <changefreq>${entry.changeFrequency}</changefreq>\n`;
      xml += `    <priority>${entry.priority}</priority>\n`;
      xml += '  </url>\n';
    }
    
    xml += '</urlset>';
    
    return xml;
  }

  /**
   * Generate robots.txt content
   */
  generateRobotsTxt(): string {
    return `User-agent: *
Allow: /

Sitemap: ${this.baseUrl}/sitemap.xml

# Block access to admin areas
Disallow: /admin/
Disallow: /api/

# Allow all search engines
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /`;
  }

  /**
   * Generate structured data for the website
   */
  generateWebsiteStructuredData() {
    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "ListyLists",
      "description": "Discover amazing places and experiences with curated travel lists",
      "url": this.baseUrl,
      "potentialAction": {
        "@type": "SearchAction",
        "target": `${this.baseUrl}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "ListyLists",
        "logo": {
          "@type": "ImageObject",
          "url": `${this.baseUrl}/logo.png`
        }
      }
    };
  }
}

export const sitemapGenerator = new SitemapGenerator();
