import { Article, ArticleListItem } from '../types/Article';

class ArticleService {
  private cache = new Map<string, Article>();
  private listCache: ArticleListItem[] | null = null;
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get all articles metadata for listing
   */
  async getArticlesList(): Promise<ArticleListItem[]> {
    if (this.listCache) {
      return this.listCache;
    }

    try {
      // In a real implementation, this would read from a directory listing
      // For now, we'll use a manifest file approach
      const response = await fetch('/articles/manifest.json');
      if (!response.ok) {
        throw new Error('Failed to load articles manifest');
      }
      
      const manifest: ArticleListItem[] = await response.json();
      this.listCache = manifest;
      return manifest;
    } catch (error) {
      console.error('Error loading articles list:', error);
      return [];
    }
  }

  /**
   * Get a specific article by slug
   */
  async getArticle(slug: string): Promise<Article | null> {
    // Check cache first
    const cacheKey = slug;
    const cachedArticle = this.cache.get(cacheKey);
    const cacheTime = this.cacheExpiry.get(cacheKey);
    
    if (cachedArticle && cacheTime && Date.now() - cacheTime < this.CACHE_DURATION) {
      return cachedArticle;
    }

    try {
      const response = await fetch(`/articles/${slug}.json`);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to load article: ${response.statusText}`);
      }

      const article: Article = await response.json();
      
      // Validate article structure
      if (!this.validateArticle(article)) {
        throw new Error('Invalid article structure');
      }

      // Cache the article
      this.cache.set(cacheKey, article);
      this.cacheExpiry.set(cacheKey, Date.now());

      return article;
    } catch (error) {
      console.error(`Error loading article ${slug}:`, error);
      return null;
    }
  }

  /**
   * Get articles by tag
   */
  async getArticlesByTag(tag: string): Promise<ArticleListItem[]> {
    const allArticles = await this.getArticlesList();
    return allArticles.filter(article => 
      tag === 'all' || article.tag === tag
    );
  }

  /**
   * Search articles by query
   */
  async searchArticles(query: string): Promise<ArticleListItem[]> {
    const allArticles = await this.getArticlesList();
    const searchTerm = query.toLowerCase();
    
    return allArticles.filter(article =>
      article.title.toLowerCase().includes(searchTerm) ||
      article.shortDescription.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get featured articles
   */
  async getFeaturedArticles(): Promise<ArticleListItem[]> {
    const allArticles = await this.getArticlesList();
    return allArticles.filter(article => article.featured);
  }

  /**
   * Get available tags
   */
  async getAvailableTags(): Promise<string[]> {
    const allArticles = await this.getArticlesList();
    const tags = new Set(allArticles.map(article => article.tag));
    return ['all', ...Array.from(tags)];
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
    this.listCache = null;
  }

  /**
   * Validate article structure
   */
  private validateArticle(article: any): article is Article {
    return (
      article &&
      typeof article.id === 'string' &&
      typeof article.slug === 'string' &&
      typeof article.title === 'string' &&
      typeof article.tag === 'string' &&
      article.content &&
      Array.isArray(article.content.places) &&
      Array.isArray(article.content.amazonProducts) &&
      article.seo &&
      typeof article.seo.title === 'string' &&
      typeof article.seo.description === 'string'
    );
  }

  /**
   * Generate sitemap data
   */
  async generateSitemapData(): Promise<{ slug: string; lastModified: string }[]> {
    const articles = await this.getArticlesList();
    return articles.map(article => ({
      slug: article.slug,
      lastModified: article.publishedDate
    }));
  }
}

export const articleService = new ArticleService();
export default ArticleService;
