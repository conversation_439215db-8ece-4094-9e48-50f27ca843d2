
import React, { useState } from 'react';
import SearchBar from '../components/SearchBar';
import TagFilter from '../components/TagFilter';
import PlaceCard from '../components/PlaceCard';
import SEOHead from '../components/SEOHead';
import { useFilteredArticles, useTags } from '../hooks/useArticles';
import { Skeleton } from '../components/ui/skeleton';

const Index = () => {
  const [selectedTag, setSelectedTag] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const { data: tags, isLoading: tagsLoading } = useTags();
  const { articles, isLoading, error, totalCount, filteredCount } = useFilteredArticles(selectedTag, searchQuery);

  // SEO metadata for the homepage
  const homepageSEO = {
    title: "ListyLists - Discover Amazing Places & Experiences",
    description: "Explore curated lists of the best restaurants, family activities, hidden gems, and shopping destinations around the world. Find your next adventure with ListyLists.",
    keywords: ["travel lists", "best places", "restaurants", "family activities", "travel guide", "hidden gems", "shopping", "travel recommendations"],
    ogTitle: "ListyLists - Your Ultimate Travel Companion",
    ogDescription: "Discover amazing places and experiences with our curated travel lists. From restaurants to family activities, find the perfect destinations for your next adventure.",
    ogImage: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=1200&h=630&fit=crop",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "ListyLists",
      "description": "Discover amazing places and experiences with curated travel lists",
      "url": "https://listylists.com",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://listylists.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
  };

  if (isLoading || tagsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <SEOHead seo={homepageSEO} />
        <header className="bg-primary/5 backdrop-blur-sm border-b border-border sticky top-0 z-40">
          <div className="container mx-auto px-4 py-6">
            <div className="text-center mb-6">
              <h1 className="text-4xl font-bold text-primary mb-2">ListyLists</h1>
              <p className="text-xl text-muted-foreground">Discover amazing places & experiences</p>
            </div>
            <Skeleton className="h-10 w-full max-w-md mx-auto" />
          </div>
        </header>
        <main className="container mx-auto px-4 py-8">
          <div className="flex gap-2 mb-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-8 w-20" />
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-card rounded-xl shadow-lg overflow-hidden">
                <Skeleton className="h-48 w-full" />
                <div className="p-4">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-full mb-3" />
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Something went wrong</h1>
          <p className="text-muted-foreground">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <SEOHead seo={homepageSEO} />

      {/* Header */}
      <header className="bg-primary/5 backdrop-blur-sm border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold text-primary mb-2">ListyLists</h1>
            <p className="text-xl text-muted-foreground">Discover amazing places & experiences</p>
            {totalCount > 0 && (
              <p className="text-sm text-muted-foreground mt-2">
                {filteredCount === totalCount
                  ? `${totalCount} curated lists`
                  : `${filteredCount} of ${totalCount} lists`
                }
              </p>
            )}
          </div>

          <SearchBar onSearch={setSearchQuery} />
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <TagFilter
          tags={tags || ['all']}
          selectedTag={selectedTag}
          onTagSelect={setSelectedTag}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {articles.map((article) => (
            <PlaceCard
              key={article.id}
              {...article}
            />
          ))}
        </div>

        {articles.length === 0 && (
          <div className="text-center py-12">
            <p className="text-xl text-muted-foreground">No places found matching your criteria.</p>
            <p className="text-muted-foreground mt-2">Try adjusting your search or tag filters.</p>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-secondary/30 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-muted-foreground">
            <p>&copy; 2024 ListyLists. Discover the world, one list at a time.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
