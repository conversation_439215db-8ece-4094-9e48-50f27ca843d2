import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useArticle } from '../hooks/useArticles';
import PlaceDetail from '../components/PlaceDetail';
import SEOHead from '../components/SEOHead';
import { Skeleton } from '../components/ui/skeleton';

const ArticlePage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { data: article, isLoading, error } = useArticle(slug || '');

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-4">
          <Skeleton className="h-8 w-32 mb-4" />
          <Skeleton className="h-12 w-3/4 mb-2" />
          <Skeleton className="h-6 w-1/2 mb-6" />
          
          <div className="grid gap-4 lg:grid-cols-3">
            <div className="lg:col-span-2 space-y-4">
              <Skeleton className="h-6 w-48 mb-3" />
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-card p-3 rounded-lg border">
                  <div className="flex gap-3">
                    <Skeleton className="w-12 h-12 rounded-md" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-5 w-3/4" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="lg:col-span-1">
              <div className="bg-secondary/20 rounded-lg p-3 border">
                <Skeleton className="h-5 w-32 mb-3" />
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-background/60 p-2 rounded border mb-2">
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return <Navigate to="/404" replace />;
  }

  const handleBack = () => {
    window.history.back();
  };

  return (
    <>
      <SEOHead seo={article.seo} />
      <PlaceDetail
        placeName={article.title}
        places={article.content.places}
        amazonProducts={article.content.amazonProducts}
        onBack={handleBack}
        article={article}
      />
    </>
  );
};

export default ArticlePage;
