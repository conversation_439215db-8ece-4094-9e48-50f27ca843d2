
import React from 'react';

interface TagFilterProps {
  tags: string[];
  selectedTag: string;
  onTagSelect: (tag: string) => void;
}

const TagFilter: React.FC<TagFilterProps> = ({ tags, selectedTag, onTagSelect }) => {
  return (
    <div className="flex flex-wrap gap-3 justify-center mb-8">
      {tags.map((tag) => (
        <button
          key={tag}
          onClick={() => onTagSelect(tag)}
          className={`px-6 py-2 rounded-full font-medium transition-all duration-200 ${
            selectedTag === tag
              ? 'bg-primary text-primary-foreground shadow-lg transform scale-105'
              : 'bg-secondary text-secondary-foreground hover:bg-accent hover:scale-105'
          }`}
        >
          {tag}
        </button>
      ))}
    </div>
  );
};

export default TagFilter;
