
import React from 'react';
import { Download } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { ArticleListItem } from '../types/Article';

interface PlaceCardProps extends ArticleListItem {
  onClick?: (id: string) => void;
}

const PlaceCard: React.FC<PlaceCardProps> = ({
  id,
  slug,
  title,
  shortDescription,
  tag,
  likes,
  image,
  publishedDate,
  featured,
  onClick
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick(id);
    } else {
      navigate(`/article/${slug}`);
    }
  };

  return (
    <div
      onClick={handleClick}
      className="bg-card rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 hover:-translate-y-1"
    >
      <div className="relative h-48 overflow-hidden">
        <img 
          src={`https://images.unsplash.com/${image}?w=400&h=300&fit=crop`}
          alt={name}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
        />
        <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-md">
          <span className="text-sm font-medium text-primary">{tag}</span>
        </div>
        {featured && (
          <div className="absolute top-3 left-3 bg-primary text-primary-foreground rounded-full px-2 py-1 shadow-md">
            <span className="text-xs font-medium">Featured</span>
          </div>
        )}
      </div>

      <div className="p-4">
        <h3 className="font-bold text-lg mb-2 text-card-foreground line-clamp-2">{title}</h3>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{shortDescription}</p>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1 text-muted-foreground">
            <Download className="h-4 w-4" />
            <span className="text-sm font-medium">{likes}</span>
          </div>
          
          <button className="text-primary hover:text-primary/80 font-medium text-sm transition-colors">
            Read more →
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlaceCard;
