
import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';

interface SearchBarProps {
  onSearch: (query: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Mock data for autocomplete - in a real app, this would come from an API
  const places = {
    en: ['London', 'Paris', 'Tokyo', 'New York', 'Barcelona', 'Rome', 'Amsterdam', 'Berlin', 'Prague', 'Vienna'],
    de: ['London', 'Paris', 'Tokio', 'New York', 'Barcelona', 'Rom', 'Amsterdam', 'Berlin', 'Prag', 'Wien'],
    es: ['Londres', 'París', 'Tokio', 'Nueva York', 'Barcelona', 'Roma', 'Ámsterdam', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Vien<PERSON>'],
    fr: ['Londres', 'Paris', 'Tokyo', 'New York', 'Barcelone', 'Rome', 'Amsterdam', 'Berlin', 'Prague', 'Vienne']
  };

  useEffect(() => {
    if (query.length > 0) {
      const browserLanguage = navigator.language.split('-')[0] as keyof typeof places;
      const placeList = places[browserLanguage] || places.en;
      const filtered = placeList.filter(place => 
        place.toLowerCase().includes(query.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [query]);

  const handleSelectSuggestion = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    onSearch(suggestion);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuggestions(false);
    onSearch(query);
  };

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search for places..."
            className="w-full pl-12 pr-4 py-4 text-lg border border-border rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-card shadow-lg"
          />
        </div>
      </form>

      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-card border border-border rounded-xl shadow-xl z-50 overflow-hidden animate-fade-in">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSelectSuggestion(suggestion)}
              className="w-full px-4 py-3 text-left hover:bg-accent transition-colors border-b border-border last:border-b-0"
            >
              <div className="flex items-center">
                <Search className="h-4 w-4 text-muted-foreground mr-3" />
                <span>{suggestion}</span>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
