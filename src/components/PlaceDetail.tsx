
import React from 'react';
import { ArrowLeft, Star, ExternalLink, MapPin, Package, Download, Clock, Globe, DollarSign } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Article, Place, AmazonProduct } from '../types/Article';

interface PlaceDetailProps {
  placeName: string;
  places: Place[];
  amazonProducts: AmazonProduct[];
  onBack: () => void;
  article?: Article;
}

const PlaceDetail: React.FC<PlaceDetailProps> = ({ placeName, places, amazonProducts, onBack, article }) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-2.5 w-2.5 ${
          i < rating ? 'text-amber-500 fill-amber-500' : 'text-gray-300'
        }`}
      />
    ));
  };

  const handleDownloadArticle = () => {
    console.log(`Downloading article for ${placeName}`);
    // Add download logic here
  };

  // Array of placeholder images for the places
  const placeImages = [
    'photo-1618160702438-9b02ab6515c9',
    'photo-1721322800607-8c38375eef04',
    'photo-1582562124811-c09040d0a901',
    'photo-1472396961693-142e6e269027'
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-4">
        <button
          onClick={onBack}
          className="flex items-center text-primary hover:text-primary/80 mb-3 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to lists
        </button>

        <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3">
          <div>
            <h1 className="text-2xl font-bold mb-1 text-foreground">{placeName}</h1>
            <p className="text-base text-muted-foreground">
              {article?.shortDescription || "Discover the best places to visit"}
            </p>
            {article && (
              <div className="flex flex-wrap gap-2 mt-2">
                <Badge variant="secondary" className="text-xs">
                  {article.tag}
                </Badge>
                {article.readingTime && (
                  <Badge variant="outline" className="text-xs flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {article.readingTime} min read
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  {article.likes} likes
                </Badge>
              </div>
            )}
          </div>
          <Button
            onClick={handleDownloadArticle}
            variant="outline"
            className="flex items-center gap-2 w-full sm:w-auto text-sm px-3 py-2 h-auto bg-muted/30 hover:bg-muted/50"
          >
            <Download className="h-4 w-4" />
            <span className="hidden xs:inline">Download Article</span>
            <span className="xs:hidden">Download</span>
          </Button>
        </div>

        {/* Introduction Section */}
        {article?.content.introduction && (
          <div className="mb-6 p-4 bg-muted/30 rounded-lg border">
            <p className="text-foreground leading-relaxed">{article.content.introduction}</p>
          </div>
        )}

        <div className="grid gap-4 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <div className="flex items-center gap-2 mb-3">
              <MapPin className="h-4 w-4 text-primary" />
              <h2 className="text-lg font-bold text-foreground">Places to Visit</h2>
              <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
                {places.length} places
              </span>
            </div>
            <div className="space-y-3">
              {places.map((place, index) => (
                  <div key={index} className="bg-card p-4 rounded-lg shadow-sm border border-border">
                    <div className="flex gap-3">
                      <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                        <img
                        src={place.image ? `/images/${place.image}.jpg` : `/images/placeholders/place-${(index % placeImages.length) + 1}.jpg`}
                        alt={place.name}
                            className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-base font-semibold text-card-foreground">{place.name}</h3>
                          <div className="flex ml-2">{renderStars(place.rating)}</div>
                        </div>

             

                        <div className="flex flex-wrap gap-1 mb-2">
                          {place.keywords.map((keyword, keywordIndex) => (
                            <Badge key={keywordIndex} variant="outline" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>



                      </div>
                    </div>
                  </div>
              ))}
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="bg-secondary/20 rounded-lg p-3 border border-border/30">
              <div className="flex items-center gap-2 mb-2">
                <Package className="h-3 w-3 text-muted-foreground" />
                <h2 className="text-sm font-medium text-muted-foreground">
                  Packing List for {placeName}
                </h2>
              </div>
              <div className="space-y-2">
                {amazonProducts.map((product, index) => (
                    <div key={index} className="bg-background/60 p-2 rounded border border-border/20">
                      <div className="flex justify-between items-start gap-2">
                        <div className="flex-1">
                          <h3 className="font-medium text-foreground text-xs leading-tight">{product.name}</h3>
                          <p className="text-primary font-semibold text-xs mt-1">{product.price}</p>
                        </div>
                        <a
                            href={product.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-primary/70 text-primary-foreground px-2 py-1 rounded text-xs hover:bg-primary transition-colors flex items-center gap-1 whitespace-nowrap"
                        >
                          <ExternalLink className="h-2 w-2" />
                          Amazon
                        </a>
                      </div>
                    </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Sections */}
        {article?.content.additionalSections && article.content.additionalSections.length > 0 && (
          <div className="mt-8 space-y-6">
            {article.content.additionalSections.map((section, index) => (
              <div key={index} className="bg-muted/20 rounded-lg p-4 border">
                <h3 className="text-lg font-semibold text-foreground mb-3">{section.title}</h3>
                <p className="text-muted-foreground leading-relaxed">{section.content}</p>
              </div>
            ))}
          </div>
        )}

        {/* Conclusion */}
        {article?.content.conclusion && (
          <div className="mt-8 p-4 bg-primary/5 rounded-lg border border-primary/20">
            <h3 className="text-lg font-semibold text-foreground mb-3">Final Thoughts</h3>
            <p className="text-foreground leading-relaxed">{article.content.conclusion}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlaceDetail;
