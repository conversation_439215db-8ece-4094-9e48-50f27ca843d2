import requests
import config  # Import the config file
import math
import re
from collections import Counter
import string
import nltk
from nltk.util import ngrams
from nltk.collocations import BigramAssocMeasures, BigramCollocationFinder

# Download required NLTK data (run once)
# nltk.download('punkt')

def extract_frequent_words(reviews, min_length=4, min_frequency=3, stop_words=None):
    """Extract and count frequent words and phrases from reviews."""
    if stop_words is None:
        # Common English stop words
        stop_words = {"when",
"always","without","after","much",
"without",
"didn",
"went",
"everything",
"thank","place","the", "and","here", "a", "to", "of", "in", "i", "is", "that", "it", "on", "you", "this",
                      "for", "but", "which", "hour", "with", "are", "have", "be", "at", "or", "was", "so", "if", "out", "not",
                      "can", "my", "your", "what", "there", "all", "we", "by", "an", "do", "me", "his", "has",
                      "just", "very", "had", "they", "from", "were", "she", "he", "as", "their", "our", "us"}

    # Combine all review texts
    all_text = " ".join([review.get("text", "") for review in reviews])

    # Convert to lowercase and remove punctuation
    all_text = all_text.lower()
    all_text = re.sub(f'[{string.punctuation}]', ' ', all_text)

    # Split into words and filter
    words = all_text.split()
    filtered_words = [word for word in words if len(word) >= min_length and word not in stop_words]

    # Count individual word frequencies
    word_counts = Counter(filtered_words)

    # Find bigrams (pairs of words that appear together)
    bigram_finder = BigramCollocationFinder.from_words(filtered_words)
    
    # Apply frequency filter to bigrams
    bigram_finder.apply_freq_filter(min_frequency)
    
    # Score bigrams using PMI (Pointwise Mutual Information)
    bigram_measures = BigramAssocMeasures()
    scored_bigrams = bigram_finder.score_ngrams(bigram_measures.pmi)
    
    # Convert bigrams to phrases and count them
    phrases = {}
    for bigram, score in scored_bigrams:
        # Only include bigrams with a meaningful association (high PMI score)
        if score > 3:  # Threshold for meaningful association
            phrase = " ".join(bigram)
            # Count occurrences of the exact phrase
            phrase_count = len(re.findall(r'\b' + re.escape(phrase) + r'\b', all_text))
            if phrase_count >= min_frequency:
                phrases[phrase] = phrase_count
                
                # Optionally, remove individual words that are part of significant phrases
                for word in bigram:
                    if word in word_counts and word_counts[word] <= phrase_count * 1.5:
                        del word_counts[word]
    
    # Combine individual words and phrases
    combined_counts = {**word_counts, **phrases}
    
    # Filter to include only items with frequency above the minimum
    frequent_items = {item: count for item, count in combined_counts.items() if count >= min_frequency}
    
    # Return top 5 items
    return dict(Counter(frequent_items).most_common(5))
