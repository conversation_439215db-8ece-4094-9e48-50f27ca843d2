import config  # Import the config file
import requests
import math
from review_analyser import *
import json
import os
from datetime import datetime
import re
import string
from collections import Counter

API_KEY = config.API_KEY

LOCATION = "27.9957142,-15.3792342"  # Latitude, Longitude of Gran Canaria
RADIUS = 1000  # Search radius in meters

excluded_types = ["dentist","beauty_salon","doctor","hair_care","gym","physiotherapist",
"health","veterinary_care","beauty_salon",
"general_contractor","real_estate_agency","car_repair", "car_wash",
"gas_station","electronics_store", "movie_theater", "home_goods_store",
"bicycle_store", "clothing_store", "store", "car_rental", "stadium",
"lodging", "restaurant", "food", "airport", "furniture_store",
"home_goods_store", "shopping_mall"]

def process_place_results(results, all_places, seen_place_ids):
    for place in results:
        # Skip places with excluded types
        place_types = place.get("types", [])
        
        if any(excluded_type in place_types for excluded_type in excluded_types):
            continue
            
        if place["place_id"] not in seen_place_ids:
            all_places.append(place)
            seen_place_ids.add(place["place_id"])
    
    return all_places, seen_place_ids

def get_top_places(location, radius, limit=1000, category=None):
    results = {}
    lat, lng = map(float, location.split(','))
    # Define a finer grid for more comprehensive results
    max_radius = min(radius, 50000)  # Max 50km per Google's limit
    # Use smaller step size for an extremely fine grid (more points)
    step_size = max_radius / 3
    grid_points = [-8 * step_size, -7 * step_size, -6 * step_size, -5 * step_size, -4 * step_size, -3 * step_size,
                   -2 * step_size, -step_size, 0,
                   step_size, 2 * step_size, 3 * step_size, 4 * step_size, 5 * step_size, 6 * step_size, 7 * step_size,
                   8 * step_size]

    # Use smaller radius for each search to ensure better coverage
    search_radius = max_radius /2

    all_places = []
    seen_place_ids = set()  # To avoid duplicates

    # Create a finer grid of points
    for lat_offset in grid_points:
        for lng_offset in grid_points:
            # Skip the center point if we've already searched it
            if lat_offset == 0 and lng_offset == 0 and len(all_places) > 0:
                continue

            # Calculate new center point
            new_lat = lat + (lat_offset / 111111)  # Approx. 111,111 meters per degree of latitude
            new_lng = lng + (lng_offset / (111111 * math.cos(math.radians(lat))))  # Adjust for longitude

            search_location = f"{new_lat},{new_lng}"

            # Build the URL - search for point_of_interest or tourist_attraction
            search_url = f"https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={search_location}&radius={search_radius}&key={API_KEY}"

            if category:
                search_url += f"&type={category}"

            response = requests.get(search_url).json()

            if "results" in response:
                # Process results using the helper function
                all_places, seen_place_ids = process_place_results(
                    response.get("results", []), all_places, seen_place_ids
                )

                next_page_token = response.get("next_page_token")
                # will probably get 3 tokens per search
                while next_page_token:
                    # Google requires a short delay before using the next_page_token
                    import time
                    time.sleep(2)
                    # Make request with page token
                    next_url = f"{search_url}&pagetoken={next_page_token}"
                    response = requests.get(next_url).json()
                    all_places, seen_place_ids = process_place_results(
                        response.get("results", []), all_places, seen_place_ids
                    )
                    next_page_token = response.get("next_page_token")

    print(50*'#',f"Total unique places found: {len(all_places)}")


    # Fetch reviews for each place
    for place in all_places:
        if "place_id" in place:
            place_id = place["place_id"]
            details_url = f"https://maps.googleapis.com/maps/api/place/details/json?place_id={place_id}&fields=reviews&key={API_KEY}"
            details_response = requests.get(details_url).json()
            
            if "result" in details_response and "reviews" in details_response["result"]:
                place["reviews"] = details_response["result"]["reviews"]
                
                # Extract and count words from reviews, requiring min frequency of 3
                word_counts = extract_frequent_words(place["reviews"], min_frequency=3)
                place["frequent_words"] = word_counts
            else:
                place["reviews"] = []
                place["frequent_words"] = {}

    # Sort using a weighted score that considers both rating and number of ratings
    def calculate_score(place):
        rating = place.get("rating", 0)
        ratings_count = place.get("user_ratings_total", 0)
        # Bayesian average approach - balance between rating and popularity
        C = 100  # Minimum ratings to be considered "reliable"
        m = 3.5  # Default rating (average on Google's 5-point scale)
        weighted_rating = (C * m + ratings_count * rating) / (C + ratings_count)
        print(rating,'###',ratings_count,'###',weighted_rating)
        return weighted_rating

    # Sort places by the calculated score
    sorted_places = sorted(
        all_places,
        key=calculate_score,
        reverse=True
    )
    top_places = sorted_places[:limit]
    
    # Create a results dictionary with metadata
    results_data = {
        "metadata": {
            "location": location,
            "radius": radius,
            "limit": limit,
            "category": category,
            "total_places_found": len(all_places),
            "timestamp": datetime.now().isoformat(),
        },
        "places": []
    }

    # Add each place to the results
    for place in top_places:
        # Extract only the fields we want to save
        place_data = {
            "name": place.get("name", "Unknown"),
            "place_id": place.get("place_id", ""),
            "location": {
                "lat": place.get("geometry", {}).get("location", {}).get("lat", 0),
                "lng": place.get("geometry", {}).get("location", {}).get("lng", 0)
            },
            "rating": place.get("rating", 0),
            "user_ratings_total": place.get("user_ratings_total", 0),
            "types": place.get("types", []),
            "vicinity": place.get("vicinity", ""),
            "keywords": list(place.get("frequent_words", {}).keys()),
            "reviews": []
        }

        # Add Google Maps link automatically
        place_data = update_existing_place_data(place_data)
        
        # Add reviews if available
        # if "reviews" in place:
        #     for review in place["reviews"]:
        #         review_data = {
        #             "author_name": review.get("author_name", "Anonymous"),
        #             "rating": review.get("rating", 0),
        #             "text": review.get("text", ""),
        #             "time": review.get("time", 0)
        #         }
        #         place_data["reviews"].append(review_data)
        
        results_data["places"].append(place_data)

    # Create output directory if it doesn't exist
    os.makedirs("results", exist_ok=True)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    category_str = f"_{category}" if category else ""
    filename = f"results/places_{timestamp}{category_str}.json"

    # Save to JSON file
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results_data, f, ensure_ascii=False, indent=2)

    print(f"Results saved to {filename}")
    print(f"Found {len(all_places)} places, saved top {len(results_data['places'])} to file")

    return filename

def generate_google_maps_link(place_id=None, name=None, lat=None, lng=None):
    """
    Generate a complete Google Maps link for a location.
    Priority: place_id > coordinates > name search
    """
    if place_id:
        # Most accurate - direct place ID link
        return f"https://www.google.com/maps/place/?q=place_id:{place_id}"
    elif lat and lng:
        # Coordinate-based link
        return f"https://www.google.com/maps/search/?api=1&query={lat},{lng}"
    elif name:
        # Name-based search (least accurate)
        encoded_name = name.replace(" ", "+").replace("&", "%26")
        return f"https://www.google.com/maps/search/?api=1&query={encoded_name}"
    else:
        return None

def add_google_links_to_files(results_directory="results"):
    """
    Iterate over all JSON files in the results directory and add Google Maps links
    to each location based on their place_id, coordinates, or name.
    """
    if not os.path.exists(results_directory):
        print(f"❌ Directory {results_directory} does not exist")
        return

    # Get all JSON files in the results directory
    json_files = [f for f in os.listdir(results_directory) if f.endswith('.json')]

    if not json_files:
        print(f"📁 No JSON files found in {results_directory}")
        return

    print(f"🔗 Processing {len(json_files)} files to add Google Maps links...")

    total_places_updated = 0

    for filename in json_files:
        file_path = os.path.join(results_directory, filename)

        try:
            # Read the JSON file
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Check if this is a places file with the expected structure
            if 'places' not in data:
                print(f"⚠️  Skipping {filename}: No 'places' key found")
                continue

            places_updated = 0

            # Process each place
            for place in data['places']:
                # Skip if google_maps_link already exists
                if 'google_maps_link' in place:
                    continue

                # Generate Google Maps link
                google_link = generate_google_maps_link(
                    place_id=place.get('place_id'),
                    name=place.get('name'),
                    lat=place.get('location', {}).get('lat'),
                    lng=place.get('location', {}).get('lng')
                )

                if google_link:
                    place['google_maps_link'] = google_link
                    places_updated += 1

            # Save the updated file if any places were updated
            if places_updated > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                print(f"✅ Updated {filename}: Added links to {places_updated} places")
                total_places_updated += places_updated
            else:
                print(f"ℹ️  {filename}: All places already have Google Maps links")

        except json.JSONDecodeError as e:
            print(f"❌ Error reading {filename}: Invalid JSON - {e}")
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")

    print(f"\n🎉 Completed! Added Google Maps links to {total_places_updated} places across {len(json_files)} files")

def update_existing_place_data(place_data):
    """
    Update a single place data dictionary with Google Maps link.
    Used when processing places in real-time.
    """
    if 'google_maps_link' not in place_data:
        google_link = generate_google_maps_link(
            place_id=place_data.get('place_id'),
            name=place_data.get('name'),
            lat=place_data.get('location', {}).get('lat'),
            lng=place_data.get('location', {}).get('lng')
        )

        if google_link:
            place_data['google_maps_link'] = google_link

    return place_data


def add_places_to_google_maps_list(place_id ,list_id, api_key=API_KEY):
    print('drin')
    # Google Maps Platform API endpoint for lists
    list_url = f"https://maps.googleapis.com/maps/api/place/lists/v1/lists/{list_id}?key={api_key}"
    # Get current list to check existing places
    try:
        print(list_url)
        current_list_response = requests.get(list_url).json()
        if "error" in current_list_response:
            print(f"❌ Error retrieving list: {current_list_response['error']['message']}")
            return current_list_response

    except Exception as e:
        print(f"❌ Error retrieving existing list: {str(e)}")
        return {"success": False, "message": str(e)}
    

    add_item_url = f"{list_url}/items?key={api_key}"
    item_data = {
        "placeId": place_id,
        "notes": f"Added via script on {datetime.now().strftime('%Y-%m-%d')}"
    }
    try:
        response = requests.post(add_item_url, json=item_data)
        if response.status_code == 200:
            print(f"✅ Added to list: { place_id}")
        else:
            print(f"❌ Failed to add {  place_id}: {response.text}")
    except Exception as e:
        print(f"❌ Error adding {  place_id}: {str(e)}")
    
    result = {
        "success": True
    }
    return result

# available_types = count_place_types(LOCATION, RADIUS)
#all_places = get_top_places(LOCATION, RADIUS)

add_places_to_google_maps_list("ChIJy3HqfzO9QAwRCGva7Hgly-E",'9YkCWusfJQCpwHss7')