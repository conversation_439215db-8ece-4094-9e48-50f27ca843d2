{"name": "google-maps-list-saver", "version": "1.0.0", "description": "Automate saving places from JSON files to Google Maps lists", "main": "google_maps_saver.js", "scripts": {"setup": "npm install", "save": "node google_maps_saver.js", "install-deps": "npm install puppeteer"}, "dependencies": {"puppeteer": "^21.0.0"}, "keywords": ["google-maps", "automation", "puppeteer", "places", "lists"], "author": "", "license": "MIT"}