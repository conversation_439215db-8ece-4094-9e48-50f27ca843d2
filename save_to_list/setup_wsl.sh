#!/bin/bash

# Google Maps List Saver Setup Script for WSL (Windows Subsystem for Linux)

echo "🚀 Setting up Google Maps List Saver for WSL..."

# Check if we're in WSL
if grep -qi microsoft /proc/version; then
    echo "✅ WSL environment detected"
else
    echo "⚠️  This doesn't appear to be WSL, but continuing anyway..."
fi

# Check if we're in the right directory
if [ ! -d "../results" ]; then
    echo "⚠️  Warning: ../results directory not found"
    echo "   Make sure you're running this from the save_to_list folder"
    echo "   and that your JSON files are in the results directory"
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Installing Node.js..."
    
    # Install Node.js using NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    if command -v node &> /dev/null; then
        echo "✅ Node.js installed successfully: $(node --version)"
    else
        echo "❌ Failed to install Node.js. Please install manually:"
        echo "   Visit: https://nodejs.org/"
        exit 1
    fi
else
    echo "✅ Node.js found: $(node --version)"
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm found: $(npm --version)"

# Install dependencies
echo "📦 Installing Puppeteer..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Check for Chrome installations
echo "🔍 Checking for Chrome installations..."

CHROME_PATHS=(
    "/mnt/c/Program Files/Google/Chrome/Application/chrome.exe"
    "/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"
    "/usr/bin/google-chrome-stable"
    "/usr/bin/google-chrome"
    "/usr/bin/chromium-browser"
    "/usr/bin/chromium"
)

CHROME_FOUND=false
for path in "${CHROME_PATHS[@]}"; do
    if [ -f "$path" ]; then
        echo "✅ Found Chrome at: $path"
        CHROME_FOUND=true
        break
    fi
done

if [ "$CHROME_FOUND" = false ]; then
    echo "⚠️  Chrome not found in common locations"
    echo "📥 Installing Chromium browser for WSL..."
    
    # Update package list
    sudo apt update
    
    # Install Chromium
    sudo apt install -y chromium-browser
    
    if command -v chromium-browser &> /dev/null; then
        echo "✅ Chromium installed successfully"
    else
        echo "⚠️  Chromium installation may have failed"
        echo "   The script will try to use Puppeteer's bundled Chromium"
    fi
fi

# Install additional dependencies for Chrome in WSL
echo "📦 Installing Chrome dependencies for WSL..."
sudo apt update

# Install essential Chrome dependencies
echo "🔧 Installing essential libraries..."
sudo apt install -y \
    libnss3 \
    libnss3-dev \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libgconf-2-4 \
    libxfixes3 \
    libxinerama1 \
    libxrandr2 \
    libxrender1 \
    libcairo-gobject2 \
    libdbus-1-3 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0

# Install additional X11 and graphics libraries
echo "🖥️  Installing X11 and graphics libraries..."
sudo apt install -y \
    xvfb \
    x11vnc \
    fluxbox \
    wmctrl \
    libxrandr2 \
    libxss1 \
    libxcursor1 \
    libxcomposite1 \
    libasound2 \
    libxi6 \
    libxtst6

# Install fonts to prevent font-related issues
echo "🔤 Installing fonts..."
sudo apt install -y \
    fonts-liberation \
    fonts-dejavu-core \
    fontconfig

echo "✅ All Chrome dependencies installed"

# Make script executable
echo "🔧 Making script executable..."
chmod +x google_maps_saver.js

echo "✅ Script is now executable"

# Count JSON files in results directory
json_count=$(find ../results -name "places_*.json" 2>/dev/null | wc -l)
echo "📄 Found $json_count place files in ../results directory"

if [ $json_count -gt 0 ]; then
    echo "📋 Available files:"
    ls -la ../results/places_*.json | head -5
    if [ $json_count -gt 5 ]; then
        echo "   ... and $((json_count - 5)) more files"
    fi
fi

# Check if DISPLAY is set for GUI applications
if [ -z "$DISPLAY" ]; then
    echo "⚠️  DISPLAY variable not set"
    echo "   For GUI applications in WSL, you may need to:"
    echo "   1. Install an X server on Windows (like VcXsrv or Xming)"
    echo "   2. Set DISPLAY variable: export DISPLAY=:0"
    echo "   3. Or use WSL2 with Windows 11 for native GUI support"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Usage examples:"
echo ""
echo "   # WSL-optimized version (recommended for profile directory issues)"
echo "   node google_maps_saver_wsl.js ../results/places_20250612_233550.json"
echo ""
echo "   # Standard version"
echo "   node google_maps_saver.js ../results/places_20250612_212737.json"
echo ""
echo "⚠️  WSL-specific notes:"
echo "   - The script will try to use Chrome from Windows if available"
echo "   - If Chrome isn't found, it will use the installed Chromium"
echo "   - You may need to set up X11 forwarding for GUI display"
echo "   - The browser window should open on your Windows desktop"
echo "   - You'll need to manually sign in to Google when prompted"
echo "   - The script uses the list_id from your JSON file automatically"
echo ""
echo "🔧 If you encounter display issues:"
echo "   export DISPLAY=:0"
echo "   # Or install WSLg (Windows 11) for native GUI support"
echo ""
