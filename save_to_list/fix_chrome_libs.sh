#!/bin/bash

# Quick fix for Chrome library dependencies in WSL
# Specifically addresses: libnss3.so: cannot open shared object file

echo "🔧 Fixing Chrome library dependencies in WSL..."

# Update package list
echo "📦 Updating package list..."
sudo apt update

# Install the specific missing library and related dependencies
echo "🔗 Installing libnss3 and related libraries..."
sudo apt install -y \
    libnss3 \
    libnss3-dev \
    libnss3-tools

# Install additional Chrome dependencies that are commonly missing
echo "📚 Installing additional Chrome dependencies..."
sudo apt install -y \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libgconf-2-4 \
    libxfixes3 \
    libxinerama1 \
    libxrender1 \
    libcairo-gobject2 \
    libdbus-1-3 \
    libgdk-pixbuf2.0-0 \
    libxcursor1 \
    libxi6 \
    libxtst6

# Install X11 libraries for GUI support
echo "🖥️  Installing X11 libraries..."
sudo apt install -y \
    xvfb \
    x11vnc \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrender1 \
    libxtst6

# Install fonts to prevent rendering issues
echo "🔤 Installing fonts..."
sudo apt install -y \
    fonts-liberation \
    fonts-dejavu-core \
    fonts-dejavu-extra \
    fontconfig

# Check if the libraries are now available
echo ""
echo "🔍 Checking if libnss3 is now available..."
if ldconfig -p | grep -q libnss3.so; then
    echo "✅ libnss3.so found!"
    ldconfig -p | grep libnss3.so
else
    echo "❌ libnss3.so still not found"
    echo "Trying alternative installation methods..."
    
    # Try installing from different sources
    sudo apt install -y libnss3:amd64
    
    # Force reinstall if needed
    sudo apt install --reinstall libnss3
fi

# Check Chrome executable
echo ""
echo "🔍 Checking Chrome installations..."

CHROME_PATHS=(
    "/mnt/c/Program Files/Google/Chrome/Application/chrome.exe"
    "/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"
    "/usr/bin/google-chrome-stable"
    "/usr/bin/google-chrome"
    "/usr/bin/chromium-browser"
    "/usr/bin/chromium"
    "$HOME/.cache/puppeteer/chrome/linux-*/chrome-linux64/chrome"
)

CHROME_FOUND=false
for path in "${CHROME_PATHS[@]}"; do
    if ls $path 2>/dev/null; then
        echo "✅ Found Chrome at: $path"
        CHROME_FOUND=true
        
        # Test if this Chrome can run
        if [[ $path == *".exe" ]]; then
            echo "   (Windows Chrome - should work)"
        else
            echo "   Testing if this Chrome can run..."
            if ldd "$path" 2>/dev/null | grep -q "not found"; then
                echo "   ⚠️  This Chrome has missing dependencies:"
                ldd "$path" 2>/dev/null | grep "not found"
            else
                echo "   ✅ This Chrome should work"
            fi
        fi
        break
    fi
done

if [ "$CHROME_FOUND" = false ]; then
    echo "⚠️  No Chrome found, installing Chromium as fallback..."
    sudo apt install -y chromium-browser
fi

# Update library cache
echo ""
echo "🔄 Updating library cache..."
sudo ldconfig

echo ""
echo "🎉 Library fix complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Try running the browser test: node test_browser.js"
echo "   2. If test passes, run: node google_maps_saver_wsl.js ../results/places_20250612_233550.json"
echo ""
echo "💡 If you still get library errors:"
echo "   - Use Windows Chrome: it should work better in WSL"
echo "   - The script will automatically try Windows Chrome first"
echo "   - Windows Chrome path: /mnt/c/Program Files/Google/Chrome/Application/chrome.exe"
echo ""
