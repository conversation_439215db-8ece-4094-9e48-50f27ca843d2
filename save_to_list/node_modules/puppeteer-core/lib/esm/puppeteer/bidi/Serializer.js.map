{"version": 3, "file": "Serializer.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Serializer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAC7C,OAAO,EAAC,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAElE,OAAO,EAAC,iBAAiB,EAAC,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAG3C;;GAEG;AACH,MAAM,mBAAoB,SAAQ,KAAK;CAAG;AAE1C;;GAEG;AACH,MAAM,OAAO,cAAc;IACzB,MAAM,CAAC,eAAe,CAAC,GAAW;QAChC,IAAI,KAAyC,CAAC;QAC9C,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,GAAG,IAAI,CAAC;QACf,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC;YACpC,KAAK,GAAG,UAAU,CAAC;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,KAAK,GAAG,WAAW,CAAC;QACtB,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,KAAK,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,GAAG,CAAC;QACd,CAAC;QACD,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,GAAkB;QACvC,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,OAAO;gBACL,IAAI,EAAE,MAAM;aACb,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACnC,OAAO,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,WAAW;aACnB,CAAC;QACJ,CAAC;aAAM,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IACE,KAAK,YAAY,SAAS;oBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE,CAAC;oBACD,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;gBACzD,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,YAAY,GAAkC,EAAE,CAAC;YACvD,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACtB,YAAY,CAAC,IAAI,CAAC;oBAChB,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC;oBACxC,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBAC9C,CAAC,CAAC;YACL,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;aAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,GAAG,CAAC,MAAM;oBACnB,KAAK,EAAE,GAAG,CAAC,KAAK;iBACjB;aACF,CAAC;QACJ,CAAC;aAAM,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO;gBACL,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE;aACzB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,mBAAmB,CAC3B,sEAAsE,CACvE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACtC,QAAQ,OAAO,GAAG,EAAE,CAAC;YACnB,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU;gBACb,MAAM,IAAI,mBAAmB,CAAC,0BAA0B,OAAO,GAAG,EAAE,CAAC,CAAC;YACxE,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAE7C,KAAK,WAAW;gBACd,OAAO;oBACL,IAAI,EAAE,WAAW;iBAClB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC7C,KAAK,QAAQ;gBACX,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE;iBACtB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG;iBACX,CAAC;YACJ,KAAK,SAAS;gBACZ,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,GAAG;iBACX,CAAC;QACN,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,SAAS,CACpB,OAAgB,EAChB,GAAY;QAEZ,IAAI,GAAG,YAAY,OAAO,EAAE,CAAC;YAC3B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QACD,kFAAkF;QAClF,MAAM,YAAY,GAChB,GAAG,IAAI,CAAC,GAAG,YAAY,YAAY,IAAI,GAAG,YAAY,iBAAiB,CAAC;YACtE,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,YAAY,EAAE,CAAC;YACjB,IACE,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE;gBACxC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,EAC7B,CAAC;gBACD,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;YACJ,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YACD,OAAO,YAAY,CAAC,WAAW,EAAiC,CAAC;QACnE,CAAC;QAED,OAAO,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC;CACF"}