# devtools-protocol [![devtools-protocol on npm](https://img.shields.io/npm/v/devtools-protocol)](https://www.npmjs.com/package/devtools-protocol)

:warning:
This repository is related to Chrome DevTools Protocol, but does not track issues regarding its definition or implementation.
If you want to file an issue for the Chrome DevTools Protocol, please open an issue on https://crbug.com under [`component: Platform>DevTools>Platform`](https://bugs.chromium.org/p/chromium/issues/entry?components=Platform%3EDevTools%3EPlatform).

Use the [protocol viewer](https://chromedevtools.github.io/devtools-protocol/) for navigating the protocol.

TypeScript definitions for the protocol's types are available in ['types/protocol.d.ts'](https://github.com/ChromeDevTools/devtools-protocol/tree/master/types). Mappings from Commands and events to these types are available in either generated `DomainApi` style in [`types/protocol-proxy-api.d.ts`](https://github.com/ChromeDevTools/devtools-protocol/blob/master/types/protocol-proxy-api.d.ts) or in simple name-to-type-interface style in [`types/protocol-mapping.d.ts`](https://github.com/ChromeDevTools/devtools-protocol/blob/master/types/protocol-mapping.d.ts).

Also, this repo is published as the [`devtools-protocol`](https://www.npmjs.com/package/devtools-protocol) npm module.
