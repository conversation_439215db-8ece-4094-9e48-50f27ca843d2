#!/usr/bin/env node

/**
 * Browser Test Script for WSL
 * Tests if P<PERSON>peteer can launch Chrome/Chromium properly in WSL environment
 */

const puppeteer = require('puppeteer');
const os = require('os');
const path = require('path');
const fs = require('fs');

async function testBrowser() {
  console.log('🧪 Testing browser setup in WSL...');
  
  // Check system info
  console.log(`📊 System info:`);
  console.log(`   OS: ${os.type()} ${os.release()}`);
  console.log(`   Platform: ${os.platform()}`);
  console.log(`   Architecture: ${os.arch()}`);
  console.log(`   Home directory: ${os.homedir()}`);
  console.log(`   Temp directory: ${os.tmpdir()}`);
  
  // Check for Chrome installations
  console.log('\n🔍 Checking for Chrome installations...');
  
  const chromePaths = [
    '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
    '/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe',
    '/usr/bin/google-chrome-stable',
    '/usr/bin/google-chrome',
    '/usr/bin/chromium-browser',
    '/usr/bin/chromium'
  ];
  
  let foundChrome = null;
  for (const chromePath of chromePaths) {
    if (fs.existsSync(chromePath)) {
      console.log(`✅ Found Chrome at: ${chromePath}`);
      foundChrome = chromePath;
      break;
    }
  }
  
  if (!foundChrome) {
    console.log('⚠️  No Chrome found in common locations, will use Puppeteer default');
  }
  
  // Test browser launch
  console.log('\n🚀 Testing browser launch...');
  
  const userDataDir = path.join(os.tmpdir(), 'test_puppeteer_profile_' + Date.now());
  console.log(`📁 Using test profile: ${userDataDir}`);
  
  const launchOptions = {
    headless: false,
    userDataDir: userDataDir,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-extensions',
      '--disable-plugins',
      '--disable-default-apps',
      '--no-first-run',
      '--no-default-browser-check',
      '--disable-translate',
      '--disable-ipc-flooding-protection',
      `--user-data-dir=${userDataDir}`
    ]
  };
  
  if (foundChrome) {
    launchOptions.executablePath = foundChrome;
  }
  
  let browser = null;
  let success = false;
  
  try {
    console.log('🔄 Attempting to launch browser...');
    browser = await puppeteer.launch(launchOptions);
    
    console.log('✅ Browser launched successfully!');
    
    // Test page creation
    const page = await browser.newPage();
    console.log('✅ Page created successfully!');
    
    // Test navigation
    console.log('🌐 Testing navigation to Google...');
    await page.goto('https://www.google.com', { waitUntil: 'networkidle2', timeout: 10000 });
    console.log('✅ Navigation successful!');
    
    // Get page title
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    success = true;
    
  } catch (error) {
    console.log('❌ Browser test failed:');
    console.log(`   Error: ${error.message}`);
    
    // Try fallback options
    console.log('\n🔄 Trying fallback configuration...');
    
    try {
      if (browser) await browser.close();
      
      const fallbackOptions = {
        headless: false,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage'
        ]
      };
      
      browser = await puppeteer.launch(fallbackOptions);
      console.log('✅ Fallback browser launched successfully!');
      
      const page = await browser.newPage();
      await page.goto('https://www.google.com', { waitUntil: 'networkidle2', timeout: 10000 });
      console.log('✅ Fallback test successful!');
      
      success = true;
      
    } catch (fallbackError) {
      console.log('❌ Fallback also failed:');
      console.log(`   Error: ${fallbackError.message}`);
    }
  }
  
  // Cleanup
  if (browser) {
    await browser.close();
    console.log('🔒 Browser closed');
  }
  
  // Clean up test profile
  try {
    if (fs.existsSync(userDataDir)) {
      fs.rmSync(userDataDir, { recursive: true, force: true });
      console.log('🧹 Cleaned up test profile');
    }
  } catch (cleanupError) {
    console.log('⚠️  Could not clean up test profile:', cleanupError.message);
  }
  
  // Results
  console.log('\n📊 Test Results:');
  if (success) {
    console.log('✅ Browser setup is working correctly!');
    console.log('🎉 You can now run the Google Maps automation script');
    console.log('\nNext step:');
    console.log('   node google_maps_saver.js ../results/places_20250612_233550.json');
  } else {
    console.log('❌ Browser setup failed');
    console.log('\n🔧 Troubleshooting suggestions:');
    console.log('1. Install Chrome: sudo apt install google-chrome-stable');
    console.log('2. Install Chromium: sudo apt install chromium-browser');
    console.log('3. Set up X11 forwarding: export DISPLAY=:0');
    console.log('4. Check WSL version: wsl --list --verbose');
    console.log('5. See WSL_TROUBLESHOOTING.md for detailed help');
  }
}

// Run the test
testBrowser().catch(console.error);
