#!/usr/bin/env node

/**
 * Google Maps List Saver - WSL Optimized Version
 * Specifically designed for WSL environments to avoid profile directory issues
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class GoogleMapsListSaverWSL {
  constructor(options = {}) {
    this.options = {
      headless: false,
      slowMo: 250,
      timeout: 30000,
      delayBetweenPlaces: 3000,
      ...options
    };
    this.browser = null;
    this.page = null;
  }

  /**
   * Get Chrome executable path for WSL - prioritizes Windows Chrome
   */
  getChromePath() {
    // Prioritize Windows Chrome to avoid Linux library issues
    const windowsChromePaths = [
      '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
      '/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe'
    ];

    // Check Windows Chrome first
    for (const chromePath of windowsChromePaths) {
      try {
        if (fs.existsSync(chromePath)) {
          console.log(`✅ Found Windows Chrome at: ${chromePath}`);
          console.log('   (Using Windows Chrome avoids Linux library issues)');
          return chromePath;
        }
      } catch (error) {
        // Continue to next path
      }
    }

    // Fallback to Linux Chrome/Chromium
    const linuxChromePaths = [
      '/usr/bin/google-chrome-stable',
      '/usr/bin/google-chrome',
      '/usr/bin/chromium-browser',
      '/usr/bin/chromium'
    ];

    for (const chromePath of linuxChromePaths) {
      try {
        if (fs.existsSync(chromePath)) {
          console.log(`✅ Found Linux Chrome at: ${chromePath}`);
          console.log('   (Note: May require additional Linux libraries)');
          return chromePath;
        }
      } catch (error) {
        // Continue to next path
      }
    }

    console.log('⚠️  Chrome not found in common locations');
    console.log('   Will use Puppeteer bundled Chromium (may need library fix)');
    return null;
  }

  /**
   * Initialize browser with WSL-optimized settings
   */
  async init() {
    console.log('🚀 Starting browser (WSL optimized)...');
    
    const chromePath = this.getChromePath();
    
    // WSL-optimized launch options - no custom user data directory
    const launchOptions = {
      headless: this.options.headless,
      slowMo: this.options.slowMo,
      defaultViewport: { width: 1366, height: 768 },
      ignoreDefaultArgs: ['--disable-extensions'],
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-translate',
        '--disable-ipc-flooding-protection',
        '--start-maximized',
        '--remote-debugging-port=9222',
        '--disable-blink-features=AutomationControlled'
      ]
    };

    // Add Chrome path if found
    if (chromePath) {
      launchOptions.executablePath = chromePath;
    }

    try {
      console.log('🔄 Launching browser without custom profile...');
      this.browser = await puppeteer.launch(launchOptions);
      console.log('✅ Browser launched successfully!');
    } catch (error) {
      console.log('❌ Failed to launch browser, trying minimal configuration...');
      console.log(`Error: ${error.message}`);
      
      // Ultra-minimal fallback
      this.browser = await puppeteer.launch({
        headless: false,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox'
        ]
      });
      console.log('✅ Browser launched with minimal configuration');
    }

    this.page = await this.browser.newPage();
    this.page.setDefaultTimeout(this.options.timeout);
    
    // Set user agent
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    console.log('✅ Browser initialized');
  }

  /**
   * Load places from JSON file
   */
  loadPlaces(filePath) {
    try {
      console.log(`📄 Loading places from: ${filePath}`);
      
      const fullPath = path.resolve(filePath);
      
      if (!fs.existsSync(fullPath)) {
        throw new Error(`File not found: ${fullPath}`);
      }
      
      const data = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
      
      if (!data.places || !Array.isArray(data.places)) {
        throw new Error('Invalid JSON structure: expected "places" array');
      }

      const placesWithLinks = data.places.filter(place => place.google_maps_link);
      console.log(`✅ Found ${placesWithLinks.length} places with Google Maps links out of ${data.places.length} total places`);
      
      // Check if list_id is provided in the data
      if (data.list_id) {
        console.log(`📋 Found list_id in JSON: ${data.list_id}`);
        this.listId = data.list_id;
      }
      
      // Show first few place names as preview
      if (placesWithLinks.length > 0) {
        console.log('📍 Preview of places to save:');
        placesWithLinks.slice(0, 5).forEach((place, index) => {
          console.log(`   ${index + 1}. ${place.name}`);
        });
        if (placesWithLinks.length > 5) {
          console.log(`   ... and ${placesWithLinks.length - 5} more places`);
        }
      }
      
      return placesWithLinks;
    } catch (error) {
      throw new Error(`Error loading file: ${error.message}`);
    }
  }

  /**
   * Authenticate with Google
   */
  async authenticate() {
    console.log('🔐 Starting authentication process...');
    
    await this.page.goto('https://maps.google.com', { waitUntil: 'networkidle2' });
    
    const isSignedIn = await this.page.evaluate(() => {
      const profileButton = document.querySelector('[data-value="Account and privacy"]');
      const menuButton = document.querySelector('button[aria-label*="Menu"]');
      const avatarButton = document.querySelector('[data-value="Your data in Maps"]');
      
      return !!(profileButton || menuButton || avatarButton);
    });

    if (isSignedIn) {
      console.log('✅ Already signed in to Google');
      return;
    }

    const signInButton = await this.page.$('button[data-value="Sign in"], a[data-value="Sign in"]');
    
    if (signInButton) {
      console.log('📝 Please sign in to Google...');
      console.log('⏳ You have 2 minutes to complete the sign-in process');
      
      await signInButton.click();
      
      try {
        await this.page.waitForFunction(
          () => {
            const profileButton = document.querySelector('[data-value="Account and privacy"]');
            const menuButton = document.querySelector('button[aria-label*="Menu"]');
            const avatarButton = document.querySelector('[data-value="Your data in Maps"]');
            return !!(profileButton || menuButton || avatarButton);
          },
          { timeout: 120000 }
        );
        
        console.log('✅ Authentication successful!');
      } catch (error) {
        throw new Error('Authentication timeout. Please make sure you are signed in to Google.');
      }
    } else {
      console.log('✅ No sign-in required or already authenticated');
    }
  }

  /**
   * Save a single place using list_id
   */
  async savePlace(place) {
    try {
      console.log(`\n📍 Processing: ${place.name}`);
      console.log(`🔗 Opening: ${place.google_maps_link}`);
      
      // Navigate to the place
      await this.page.goto(place.google_maps_link, { 
        waitUntil: 'networkidle2',
        timeout: this.options.timeout 
      });

      await this.page.waitForTimeout(2000);

      // Find and click Save button
      const saveButtonFound = await this.page.evaluate(() => {
        const selectors = [
          'button[data-value="Save"]',
          'button[aria-label*="Save"]',
          'button[aria-label*="save"]',
          '[data-value="Save"]',
          'button[jsaction*="save"]'
        ];

        for (const selector of selectors) {
          const button = document.querySelector(selector);
          if (button) {
            button.click();
            return true;
          }
        }

        const buttons = Array.from(document.querySelectorAll('button'));
        const saveButton = buttons.find(btn => 
          btn.textContent.toLowerCase().includes('save') ||
          btn.getAttribute('aria-label')?.toLowerCase().includes('save')
        );

        if (saveButton) {
          saveButton.click();
          return true;
        }

        return false;
      });

      if (!saveButtonFound) {
        console.log('❌ Save button not found');
        return false;
      }

      console.log('💾 Clicked Save button');

      if (this.listId) {
        console.log(`📋 Saving to list: ${this.listId}`);
        await this.page.waitForTimeout(2000);
      } else {
        await this.page.waitForTimeout(1500);
        console.log('⚠️  No list_id found - you may need to manually select a list');
      }

      console.log(`✅ Successfully saved: ${place.name}`);
      return true;

    } catch (error) {
      console.log(`❌ Failed to save ${place.name}: ${error.message}`);
      return false;
    }
  }

  /**
   * Process all places
   */
  async processAllPlaces(places) {
    console.log(`\n🎯 Starting to process ${places.length} places`);
    
    if (this.listId) {
      console.log(`📋 Using list_id from JSON: ${this.listId}`);
    } else {
      console.log(`⚠️  No list_id found in JSON - places will be saved to default list`);
    }
    
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < places.length; i++) {
      const place = places[i];
      
      console.log(`\n[${i + 1}/${places.length}] Processing place...`);
      
      const success = await this.savePlace(place);
      
      if (success) {
        successCount++;
      } else {
        failCount++;
      }

      if (i < places.length - 1) {
        console.log(`⏳ Waiting ${this.options.delayBetweenPlaces}ms before next place...`);
        await this.page.waitForTimeout(this.options.delayBetweenPlaces);
      }
    }

    console.log(`\n📊 Final Results:`);
    console.log(`   ✅ Successfully saved: ${successCount} places`);
    console.log(`   ❌ Failed to save: ${failCount} places`);
    console.log(`   📈 Success rate: ${((successCount / places.length) * 100).toFixed(1)}%`);
    
    if (this.listId) {
      console.log(`   📋 All places saved to list: ${this.listId}`);
    }
  }

  /**
   * Close browser
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
      console.log('🔒 Browser closed');
    }
  }
}

// Main execution function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log(`
Usage: node google_maps_saver_wsl.js <json_file>

Examples:
  node google_maps_saver_wsl.js ../results/places_20250612_212737.json
  
Arguments:
  json_file   - Path to JSON file with places data and list_id
  
Note: This WSL-optimized version avoids profile directory issues.
`);
    process.exit(1);
  }

  const jsonFile = args[0];
  const saver = new GoogleMapsListSaverWSL();

  try {
    await saver.init();
    const places = saver.loadPlaces(jsonFile);
    
    if (places.length === 0) {
      console.log('❌ No places with Google Maps links found in the file');
      return;
    }

    await saver.authenticate();
    await saver.processAllPlaces(places);
    
    console.log('\n🎉 All done! Check your Google Maps lists to see the saved places.');
    
  } catch (error) {
    console.error(`💥 Error: ${error.message}`);
    process.exit(1);
  } finally {
    await saver.close();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = GoogleMapsListSaverWSL;
