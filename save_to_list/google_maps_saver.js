#!/usr/bin/env node

/**
 * Google Maps List Saver
 * Automates saving places from JSON files to Google Maps lists
 * 
 * Usage:
 * node save_to_list/google_maps_saver.js results/places_20250612_212737.json "My Travel List"
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class GoogleMapsListSaver {
  constructor(options = {}) {
    this.options = {
      headless: false, // Keep browser visible for manual intervention if needed
      slowMo: 250,     // Slow down actions for better reliability
      timeout: 30000,  // 30 second timeout
      delayBetweenPlaces: 3000, // 3 seconds between places
      ...options
    };
    this.browser = null;
    this.page = null;
  }

  /**
   * Get Chrome executable path for WSL
   */
  getChromePath() {
    const possiblePaths = [
      // Windows Chrome paths accessible from WSL
      '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
      '/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe',
      // Linux Chrome paths
      '/usr/bin/google-chrome-stable',
      '/usr/bin/google-chrome',
      '/usr/bin/chromium-browser',
      '/usr/bin/chromium',
      // Snap Chrome
      '/snap/bin/chromium',
      // Flatpak Chrome
      '/var/lib/flatpak/exports/bin/com.google.Chrome'
    ];

    const fs = require('fs');

    for (const path of possiblePaths) {
      try {
        if (fs.existsSync(path)) {
          console.log(`✅ Found Chrome at: ${path}`);
          return path;
        }
      } catch (error) {
        // Continue to next path
      }
    }

    console.log('⚠️  Chrome not found in common locations, using Puppeteer default');
    return null;
  }

  /**
   * Initialize browser
   */
  async init() {
    console.log('🚀 Starting browser...');

    const chromePath = this.getChromePath();
    const os = require('os');
    const path = require('path');

    // Create a custom user data directory in a writable location
    this.userDataDir = path.join(os.tmpdir(), 'puppeteer_chrome_profile_' + Date.now());

    // Ensure the directory exists and is writable
    try {
      const fs = require('fs');
      if (!fs.existsSync(this.userDataDir)) {
        fs.mkdirSync(this.userDataDir, { recursive: true, mode: 0o755 });
        console.log(`📁 Created user data directory: ${this.userDataDir}`);
      }
    } catch (dirError) {
      console.log(`⚠️  Could not create user data directory: ${dirError.message}`);
      // Fallback to home directory
      this.userDataDir = path.join(os.homedir(), '.puppeteer_chrome_profile_' + Date.now());
      try {
        const fs = require('fs');
        fs.mkdirSync(this.userDataDir, { recursive: true, mode: 0o755 });
        console.log(`📁 Created fallback user data directory: ${this.userDataDir}`);
      } catch (fallbackError) {
        console.log(`⚠️  Could not create fallback directory: ${fallbackError.message}`);
        this.userDataDir = null;
      }
    }

    const launchOptions = {
      headless: this.options.headless,
      slowMo: this.options.slowMo,
      defaultViewport: { width: 1366, height: 768 },
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-default-apps',
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-translate',
        '--disable-ipc-flooding-protection',
        '--start-maximized'
      ]
    };

    // Add user data directory if available
    if (this.userDataDir) {
      launchOptions.userDataDir = this.userDataDir;
      launchOptions.args.push(`--user-data-dir=${this.userDataDir}`);
    }

    // Add Chrome path if found
    if (chromePath) {
      launchOptions.executablePath = chromePath;
    }

    try {
      console.log(`📁 Using user data directory: ${this.userDataDir}`);
      this.browser = await puppeteer.launch(launchOptions);
    } catch (error) {
      console.log('❌ Failed to launch browser with custom profile, trying alternatives...');
      console.log(`Error: ${error.message}`);

      // Try without executable path and custom profile
      delete launchOptions.executablePath;
      delete launchOptions.userDataDir;

      // Remove user-data-dir from args
      launchOptions.args = launchOptions.args.filter(arg => !arg.startsWith('--user-data-dir'));

      try {
        this.browser = await puppeteer.launch(launchOptions);
      } catch (secondError) {
        console.log('❌ Still failed, trying minimal configuration...');
        console.log(`Error: ${secondError.message}`);

        // Last resort: minimal options with temp directory
        this.userDataDir = path.join(os.homedir(), '.puppeteer_temp_' + Date.now());

        this.browser = await puppeteer.launch({
          headless: false,
          userDataDir: this.userDataDir,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            `--user-data-dir=${this.userDataDir}`
          ]
        });
      }
    }

    this.page = await this.browser.newPage();
    this.page.setDefaultTimeout(this.options.timeout);

    // Set user agent to avoid detection
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    console.log('✅ Browser initialized');
  }

  /**
   * Load places from JSON file
   */
  loadPlaces(filePath) {
    try {
      console.log(`📄 Loading places from: ${filePath}`);

      // Resolve path relative to project root
      const fullPath = path.resolve(filePath);

      if (!fs.existsSync(fullPath)) {
        throw new Error(`File not found: ${fullPath}`);
      }

      const data = JSON.parse(fs.readFileSync(fullPath, 'utf8'));

      if (!data.places || !Array.isArray(data.places)) {
        throw new Error('Invalid JSON structure: expected "places" array');
      }

      const placesWithLinks = data.places.filter(place => place.google_maps_link);
      console.log(`✅ Found ${placesWithLinks.length} places with Google Maps links out of ${data.places.length} total places`);

      // Check if list_id is provided in the data
      if (data.list_id) {
        console.log(`📋 Found list_id in JSON: ${data.list_id}`);
        this.listId = data.list_id;
      }

      // Show first few place names as preview
      if (placesWithLinks.length > 0) {
        console.log('📍 Preview of places to save:');
        placesWithLinks.slice(0, 5).forEach((place, index) => {
          console.log(`   ${index + 1}. ${place.name}`);
        });
        if (placesWithLinks.length > 5) {
          console.log(`   ... and ${placesWithLinks.length - 5} more places`);
        }
      }

      return placesWithLinks;
    } catch (error) {
      throw new Error(`Error loading file: ${error.message}`);
    }
  }

  /**
   * Authenticate with Google (manual process)
   */
  async authenticate() {
    console.log('🔐 Starting authentication process...');
    
    // Go to Google Maps
    await this.page.goto('https://maps.google.com', { waitUntil: 'networkidle2' });
    
    // Check if already signed in by looking for profile/menu button
    const isSignedIn = await this.page.evaluate(() => {
      // Look for common signed-in indicators
      const profileButton = document.querySelector('[data-value="Account and privacy"]');
      const menuButton = document.querySelector('button[aria-label*="Menu"]');
      const avatarButton = document.querySelector('[data-value="Your data in Maps"]');
      
      return !!(profileButton || menuButton || avatarButton);
    });

    if (isSignedIn) {
      console.log('✅ Already signed in to Google');
      return;
    }

    // Look for sign-in button
    const signInButton = await this.page.$('button[data-value="Sign in"], a[data-value="Sign in"]');
    
    if (signInButton) {
      console.log('📝 Please sign in to Google...');
      console.log('⏳ You have 2 minutes to complete the sign-in process');
      console.log('   1. The browser will open the sign-in page');
      console.log('   2. Sign in with your Google account');
      console.log('   3. Make sure you can access Google Maps');
      console.log('   4. The script will continue automatically');
      
      await signInButton.click();
      
      // Wait for authentication to complete (2 minutes max)
      try {
        await this.page.waitForFunction(
          () => {
            const profileButton = document.querySelector('[data-value="Account and privacy"]');
            const menuButton = document.querySelector('button[aria-label*="Menu"]');
            const avatarButton = document.querySelector('[data-value="Your data in Maps"]');
            return !!(profileButton || menuButton || avatarButton);
          },
          { timeout: 120000 } // 2 minutes
        );
        
        console.log('✅ Authentication successful!');
      } catch (error) {
        throw new Error('Authentication timeout. Please make sure you are signed in to Google.');
      }
    } else {
      console.log('✅ No sign-in required or already authenticated');
    }
  }

  /**
   * Save a single place to Google Maps list using list_id
   */
  async savePlace(place) {
    try {
      console.log(`\n📍 Processing: ${place.name}`);
      console.log(`🔗 Opening: ${place.google_maps_link}`);

      // If we have a list_id, construct the save URL directly
      if (this.listId) {
        const saveUrl = `${place.google_maps_link}&list=${this.listId}`;
        console.log(`📋 Using list_id: ${this.listId}`);

        // Navigate to the place with list parameter
        await this.page.goto(saveUrl, {
          waitUntil: 'networkidle2',
          timeout: this.options.timeout
        });
      } else {
        // Navigate to the place normally
        await this.page.goto(place.google_maps_link, {
          waitUntil: 'networkidle2',
          timeout: this.options.timeout
        });
      }

      // Wait for page to fully load
      await this.page.waitForTimeout(2000);

      // Look for the Save button with multiple selectors
      const saveButtonFound = await this.page.evaluate(() => {
        // Try different selectors for the Save button
        const selectors = [
          'button[data-value="Save"]',
          'button[aria-label*="Save"]',
          'button[aria-label*="save"]',
          '[data-value="Save"]',
          'button[jsaction*="save"]'
        ];

        for (const selector of selectors) {
          const button = document.querySelector(selector);
          if (button) {
            button.click();
            return true;
          }
        }

        // Fallback: look for button with "Save" text
        const buttons = Array.from(document.querySelectorAll('button'));
        const saveButton = buttons.find(btn =>
          btn.textContent.toLowerCase().includes('save') ||
          btn.getAttribute('aria-label')?.toLowerCase().includes('save')
        );

        if (saveButton) {
          saveButton.click();
          return true;
        }

        return false;
      });

      if (!saveButtonFound) {
        console.log('❌ Save button not found');
        return false;
      }

      console.log('💾 Clicked Save button');

      // If we have a list_id, the place should be saved directly to that list
      if (this.listId) {
        console.log(`📋 Saving directly to list: ${this.listId}`);
        // Wait for save operation to complete
        await this.page.waitForTimeout(2000);
      } else {
        // Wait for the save dialog/menu to appear and handle manual selection
        await this.page.waitForTimeout(1500);
        console.log('⚠️  No list_id found - you may need to manually select a list');
      }

      console.log(`✅ Successfully saved: ${place.name}`);
      return true;

    } catch (error) {
      console.log(`❌ Failed to save ${place.name}: ${error.message}`);
      return false;
    }
  }



  /**
   * Process all places
   */
  async processAllPlaces(places) {
    console.log(`\n🎯 Starting to process ${places.length} places`);

    if (this.listId) {
      console.log(`📋 Using list_id from JSON: ${this.listId}`);
    } else {
      console.log(`⚠️  No list_id found in JSON - places will be saved to default list`);
    }

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < places.length; i++) {
      const place = places[i];

      console.log(`\n[${i + 1}/${places.length}] Processing place...`);

      const success = await this.savePlace(place);

      if (success) {
        successCount++;
      } else {
        failCount++;
      }

      // Add delay between places to avoid rate limiting
      if (i < places.length - 1) {
        console.log(`⏳ Waiting ${this.options.delayBetweenPlaces}ms before next place...`);
        await this.page.waitForTimeout(this.options.delayBetweenPlaces);
      }
    }

    console.log(`\n📊 Final Results:`);
    console.log(`   ✅ Successfully saved: ${successCount} places`);
    console.log(`   ❌ Failed to save: ${failCount} places`);
    console.log(`   📈 Success rate: ${((successCount / places.length) * 100).toFixed(1)}%`);

    if (this.listId) {
      console.log(`   📋 All places saved to list: ${this.listId}`);
    }
  }

  /**
   * Close browser and cleanup
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
      console.log('🔒 Browser closed');
    }

    // Cleanup temporary directories
    if (this.userDataDir) {
      try {
        const fs = require('fs');
        const path = require('path');

        // Remove temporary profile directory
        if (fs.existsSync(this.userDataDir)) {
          fs.rmSync(this.userDataDir, { recursive: true, force: true });
          console.log('🧹 Cleaned up temporary profile directory');
        }
      } catch (error) {
        console.log('⚠️  Could not clean up temporary directory:', error.message);
      }
    }
  }
}

// Main execution function
async function main() {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    console.log(`
Usage: node save_to_list/google_maps_saver.js <json_file>

Examples:
  node save_to_list/google_maps_saver.js results/places_20250612_212737.json

Arguments:
  json_file   - Path to JSON file with places data and list_id

Note: The script will use the list_id from the JSON file if available.
`);
    process.exit(1);
  }

  const jsonFile = args[0];

  const saver = new GoogleMapsListSaver();

  try {
    // Initialize browser
    await saver.init();

    // Load places from JSON (this will also set the list_id if present)
    const places = saver.loadPlaces(jsonFile);

    if (places.length === 0) {
      console.log('❌ No places with Google Maps links found in the file');
      return;
    }

    // Authenticate with Google
    await saver.authenticate();

    // Process all places
    await saver.processAllPlaces(places);

    console.log('\n🎉 All done! Check your Google Maps lists to see the saved places.');

  } catch (error) {
    console.error(`💥 Error: ${error.message}`);
    process.exit(1);
  } finally {
    await saver.close();
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = GoogleMapsListSaver;
