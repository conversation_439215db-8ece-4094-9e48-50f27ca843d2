# WSL Troubleshooting Guide

This guide helps resolve common issues when running the Google Maps automation script in WSL (Windows Subsystem for Linux).

## 🚀 Quick Setup for WSL

```bash
# Use the WSL-specific setup script
chmod +x setup_wsl.sh
./setup_wsl.sh
```

## 🔧 Common WSL Issues and Solutions

### 1. Chrome Profile Directory Error

**Error**: `Google Chrome kann weder lesen noch schrieben tmp/puppeteer_dev_chrome_profile-XXXXX`
(Chrome cannot read or write to the temporary profile directory)

**Solution**: The script now automatically handles this by:
- Creating a custom user data directory in a writable location
- Using proper permissions for WSL environment
- Cleaning up temporary directories after use

If you still get this error, try:
```bash
# Create a writable temp directory
mkdir -p ~/tmp/chrome_profiles
export TMPDIR=~/tmp/chrome_profiles

# Run the script
node google_maps_saver.js ../results/places_20250612_233550.json
```

### 2. Browser Not Found Error

**Error**: `Error: Failed to launch the browser process!`

**Solutions**:

#### Option A: Use Windows Chrome (Recommended)
```bash
# The script automatically detects Windows Chrome at:
# /mnt/c/Program Files/Google/Chrome/Application/chrome.exe
# /mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe
```

#### Option B: Install Chromium in WSL
```bash
sudo apt update
sudo apt install chromium-browser
```

#### Option C: Install Chrome in WSL
```bash
# Download Chrome for Linux
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list'
sudo apt update
sudo apt install google-chrome-stable
```

### 2. Display Issues (No GUI)

**Error**: Browser doesn't open or display errors

**Solutions**:

#### Option A: WSL2 with Windows 11 (Easiest)
Windows 11 with WSL2 supports GUI applications natively:
```bash
# Just run the script - GUI should work automatically
node google_maps_saver.js ../results/places_20250612_233550.json
```

#### Option B: X11 Forwarding (Windows 10)
1. **Install X Server on Windows**:
   - Download VcXsrv: https://sourceforge.net/projects/vcxsrv/
   - Or Xming: https://sourceforge.net/projects/xming/

2. **Configure X Server**:
   - Start VcXsrv with "Disable access control" checked
   - Set display number to 0

3. **Set DISPLAY in WSL**:
   ```bash
   export DISPLAY=:0
   # Or for WSL2:
   export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
   ```

4. **Test X11**:
   ```bash
   sudo apt install x11-apps
   xclock  # Should open a clock window
   ```

#### Option C: Use Windows Chrome with WSL
```bash
# Create a wrapper script to use Windows Chrome
echo '#!/bin/bash' > /usr/local/bin/chrome
echo '/mnt/c/Program\ Files/Google/Chrome/Application/chrome.exe "$@"' >> /usr/local/bin/chrome
chmod +x /usr/local/bin/chrome
```

### 3. Permission Issues

**Error**: `EACCES: permission denied`

**Solutions**:
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules

# Or use npm prefix
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 4. Missing Libraries Error

**Error**: `libnss3.so: cannot open shared object file: No such file or directory`

**Quick Fix**:
```bash
# Run the library fix script
chmod +x fix_chrome_libs.sh
./fix_chrome_libs.sh
```

**Manual Fix**:
```bash
# Install the missing library
sudo apt update
sudo apt install -y libnss3 libnss3-dev libnss3-tools

# Install additional Chrome dependencies
sudo apt install -y \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libgconf-2-4 \
    libxfixes3 \
    libxinerama1 \
    libxrender1 \
    libcairo-gobject2 \
    libdbus-1-3 \
    libgdk-pixbuf2.0-0

# Update library cache
sudo ldconfig
```

**Alternative Solution - Use Windows Chrome**:
The script automatically tries Windows Chrome first, which avoids Linux library issues:
```bash
# Windows Chrome should be automatically detected at:
# /mnt/c/Program Files/Google/Chrome/Application/chrome.exe
```

### 5. Network Issues

**Error**: Cannot connect to Google Maps

**Solutions**:
```bash
# Check DNS resolution
nslookup maps.google.com

# If DNS issues, try:
echo "nameserver *******" | sudo tee /etc/resolv.conf

# Or use Windows DNS:
echo "nameserver $(cat /etc/resolv.conf | grep nameserver | awk '{print $2}')" | sudo tee /etc/resolv.conf
```

## 🎯 Recommended WSL Setup

### For Windows 11 Users:
1. Use WSL2 with GUI support
2. Install the script dependencies
3. Run directly - no X server needed

### For Windows 10 Users:
1. Install WSL2
2. Install VcXsrv X server on Windows
3. Set up X11 forwarding
4. Use Windows Chrome through WSL

## 🔍 Testing Your Setup

### Test 1: Check Node.js
```bash
node --version
npm --version
```

### Test 2: Check Chrome
```bash
# Test if Chrome can be found
ls -la /mnt/c/Program\ Files/Google/Chrome/Application/chrome.exe
# or
which chromium-browser
```

### Test 3: Test X11 (if using X server)
```bash
export DISPLAY=:0
xclock
```

### Test 4: Run Browser Test Script
```bash
# Use the included test script
node test_browser.js
```

This test script will:
- Check your system configuration
- Find available Chrome installations
- Test browser launching with proper WSL settings
- Verify navigation works
- Clean up temporary files

### Test 5: Test Puppeteer (Manual)
```bash
node -e "const puppeteer = require('puppeteer'); puppeteer.launch({headless: false}).then(browser => { console.log('✅ Puppeteer works!'); browser.close(); });"
```

## 🚀 Running the Script

Once everything is set up:

```bash
# Navigate to the script directory
cd save_to_list

# Run the script
node google_maps_saver.js ../results/places_20250612_233550.json
```

## 📝 Environment Variables

Add these to your `~/.bashrc` for persistent settings:

```bash
# For X11 forwarding (Windows 10)
export DISPLAY=:0

# For WSL2 with X11 (Windows 10)
export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0

# For better Chrome performance
export CHROME_DEVEL_SANDBOX=/usr/local/sbin/chrome-devel-sandbox
```