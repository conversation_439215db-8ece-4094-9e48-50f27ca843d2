import fs from 'fs';
import path from 'path';

/**
 * Vite plugin to automatically generate articles manifest
 */
export function articlesManifestPlugin() {
  let isProduction = false;

  return {
    name: 'articles-manifest',
    configResolved(config) {
      isProduction = config.command === 'build';
    },
    buildStart() {
      // Generate manifest at build start
      generateManifest();
    },
    configureServer(server) {
      // Watch articles directory in development
      const articlesDir = path.resolve('public/articles');
      
      server.watcher.add(articlesDir);
      server.watcher.on('change', (file) => {
        if (file.includes('public/articles') && file.endsWith('.json') && !file.includes('manifest.json')) {
          console.log('📝 Article changed, regenerating manifest...');
          generateManifest();
          
          // Trigger HMR update
          server.ws.send({
            type: 'full-reload'
          });
        }
      });

      server.watcher.on('add', (file) => {
        if (file.includes('public/articles') && file.endsWith('.json') && !file.includes('manifest.json')) {
          console.log('📝 New article added, regenerating manifest...');
          generateManifest();
          
          // Trigger HMR update
          server.ws.send({
            type: 'full-reload'
          });
        }
      });
    }
  };
}

function generateManifest() {
  try {
    const articlesDir = path.resolve('public/articles');
    const manifestPath = path.join(articlesDir, 'manifest.json');

    if (!fs.existsSync(articlesDir)) {
      console.warn('⚠️  Articles directory not found');
      return;
    }

    const files = fs.readdirSync(articlesDir);
    const articleFiles = files.filter(file => 
      file.endsWith('.json') && file !== 'manifest.json'
    );

    const manifestEntries = [];

    for (const file of articleFiles) {
      const filePath = path.join(articlesDir, file);
      
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const article = JSON.parse(content);

        if (!article.id || !article.slug || !article.title) {
          console.warn(`⚠️  Skipping ${file}: Missing required fields`);
          continue;
        }

        const manifestEntry = {
          id: article.id,
          slug: article.slug,
          title: article.title,
          shortDescription: article.shortDescription || '',
          tag: article.tag || 'general',
          likes: article.likes || 0,
          image: article.image || '',
          publishedDate: article.publishedDate || new Date().toISOString(),
          featured: article.featured || false
        };

        manifestEntries.push(manifestEntry);

      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message);
      }
    }

    // Sort by publication date (newest first)
    manifestEntries.sort((a, b) => 
      new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime()
    );

    fs.writeFileSync(
      manifestPath, 
      JSON.stringify(manifestEntries, null, 2),
      'utf8'
    );

    console.log(`✅ Generated manifest with ${manifestEntries.length} articles`);

  } catch (error) {
    console.error('❌ Error generating manifest:', error);
  }
}
