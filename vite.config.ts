import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { articlesManifestPlugin } from "./vite-plugins/articles-manifest.js";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    allowedHosts: [
      'listmymaps.diffus.org'
    ],
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    articlesManifestPlugin(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
